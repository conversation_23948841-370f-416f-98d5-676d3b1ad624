@startuml
title GPU巡检流程详细架构图

actor "用户" as User
participant "前端界面" as Frontend
participant "GpuInspectionController" as Controller
participant "ServiceGpuInspection" as Service
participant "GpuInspection" as Domain
participant "ServicePrechecker" as Prechecker
participant "ServiceDeployment" as Deployment
participant "CacheService" as Cache
database "ConfigMap" as ConfigMap
database "内存状态" as Memory

== 启动GPU巡检任务 ==
User -> Frontend: 发起GPU巡检请求
Frontend -> Controller: POST /apts/gpuinspectiontask
note right: GpuInspectionTaskInfo参数
Controller -> Controller: 解析请求参数
Controller -> Service: StartGpuInspection()

Service -> Cache: 获取集群已使用GPU
Service -> Service: 获取集群GPU列表
Service -> Service: 过滤可用GPU
Service -> Service: 计算诊断类型和数量
Service -> Domain: StartGpuInspection()

Domain -> Domain: 检查任务状态
alt 有运行中任务
    Domain --> Controller: 返回错误"task is running"
else 有删除中任务
    Domain --> Controller: 返回错误"task is deleting"
else 无冲突任务
    Domain -> Domain: 继续执行
end

Domain -> Domain: CleanExistCacheInfo()
Domain -> Deployment: GetAptsCmGpuInspectionTaskids()
Deployment --> Domain: 返回现有任务ID列表

alt 无现有任务
    Domain -> Domain: StartOriginGpuInspectionTask()
else 有现有任务
    Domain -> Domain: StartExistGpuInspectionTask()
    Domain -> Domain: 检查现有任务状态
    alt 任务已完成
        Domain -> Domain: 清理并启动新任务
    else 任务运行中
        Domain -> Domain: DeleteGpuInspection()
        Domain --> Controller: 返回"task is deleting"
    end
end

== 任务初始化和执行 ==
Domain -> Memory: 初始化任务摘要
note right: gpuInspectionSummary
Domain -> Memory: 更新占用GPU信息
Domain -> Domain: 启动异步任务QueryAllGpuInspectionTasks()

par 异步执行
    Domain -> Domain: CreateGpuInspectionTask()
    Domain -> Domain: SplitDiagTasks()
    note right: 每组最多50个GPU

    loop 为每个任务组
        Domain -> Prechecker: StartPrecheck()
        note right: 创建诊断任务
        Prechecker --> Domain: 返回任务ID
    end

    Domain -> Deployment: UpdateAptsCmGpuInspectionTaskids()
    note right: 持久化任务ID到ConfigMap

    Domain -> Domain: 启动巡检循环

    loop 巡检轮次
        Domain -> Domain: RunInspectionLoop()

        loop 定时检查
            Domain -> Domain: ProcessGpuInspectionRunningTasks()
            Domain -> Domain: CompletedTasks()
            Domain -> Prechecker: GetDiagTaskInfo()
            Domain -> Memory: UpdateGpuInspectionSummary()
            Domain -> Memory: UpdateUnhealthyGpus()

            alt 所有任务完成
                Domain -> Domain: 退出循环
            else 收到停止信号
                Domain -> Domain: 停止巡检
            else 继续监控
                Domain -> Domain: 等待下次检查
            end
        end

        Domain -> Domain: DeleteAllGpuInspectionTasks()
        Domain -> Deployment: CleanConfigmap()

        alt 还有剩余轮次
            Domain -> Domain: 开始下一轮次
        else 所有轮次完成
            Domain -> Domain: UpdateCompletedGpuInspectionSummary()
        end
    end
end

Domain --> Controller: 返回成功
Controller --> Frontend: 返回响应
Frontend --> User: 显示任务启动成功

== 查询巡检状态 ==
User -> Frontend: 查询巡检状态
Frontend -> Controller: GET /apts/gpuinspectiontask
Controller -> Service: QueryGpuInspection()
Service -> Domain: QueryGpuInspection()
Domain -> Memory: 读取gpuInspectionSummary
Domain -> Memory: 读取unhealthyGpusInfo
Domain -> Domain: 构建QueryDiagTask响应
Domain --> Controller: 返回任务状态和异常GPU信息
Controller --> Frontend: 返回JSON响应
Frontend --> User: 显示巡检进度和结果

== 删除/取消巡检任务 ==
User -> Frontend: 取消巡检任务
Frontend -> Controller: DELETE /apts/gpuinspectiontask
Controller -> Service: DeleteGpuInspection()
Service -> Domain: DeleteGpuInspection()

Domain -> Memory: 发送停止信号到actionChan[RUNCHAN]
Domain -> Domain: CleanExistCacheInfo()
Domain -> Deployment: CleanConfigmap()

par 异步删除
    Domain -> Domain: DeleteAllGpuInspectionTasks()
    loop 删除重试
        Domain -> Prechecker: DeleteDiagTask()
        Domain -> Domain: 检查是否还有运行中任务
        alt 删除成功
            Domain -> Domain: 退出循环
        else 删除失败
            Domain -> Domain: 重试删除
        end
    end
end

Domain --> Controller: 返回成功
Controller --> Frontend: 返回响应
Frontend --> User: 显示删除成功

@enduml